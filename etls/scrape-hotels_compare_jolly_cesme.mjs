import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

const desti = 'cesme';
const destiJson = `jolly_hotels_${desti}-otelleri_2025.07.27_2025.08.03.json`;

const fnSupport = {
    normalizeJsonObject: (jsonObj) => {
        const normalizedObj = {};
        for (const key in jsonObj) {
            if (Object.prototype.hasOwnProperty.call(jsonObj, key)) {
                let value = jsonObj[key];
                // Convert null or undefined to empty string before normalization
                if (value === null || value === undefined) {
                    value = '';
                }
                normalizedObj[key] = typeof value === 'string' ? fnSupport.normalizeText(value) : value;
            }
        }
        return normalizedObj;
    },
    normalizeText: (text) => {
        if (!text || typeof text !== 'string') return '';
        let cleaned = text;
        // HTML etiketleri ve özel karakterler temizleniyor
        cleaned = cleaned
            .replace(/<[^>]*>/g, ' ')
            .replace(/&nbsp;/gi, ' ')
            .replace(/&amp;/gi, '&')
            .replace(/</gi, '<')
            .replace(/>/gi, '>')
            .replace(/&quot;/gi, '"')
            .replace(/&#39;/gi, "'")
            .replace(/&apos;/gi, "'")
            .replace(/&[a-zA-Z0-9#]+;/g, ' ')
            .replace(/[\t\n\r]/g, ' ')
            .replace(/[\u0000-\u001F\u007F]/g, '')
            .replace(/\s+/g, ' ');

        // Türkçe karakter dönüşümü
        cleaned = cleaned
            .replace(/ç/g, 'c').replace(/Ç/g, 'C')
            .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
            .replace(/ı/g, 'i').replace(/İ/g, 'I')
            .replace(/ö/g, 'o').replace(/Ö/g, 'O')
            .replace(/ş/g, 's').replace(/Ş/g, 'S')
            .replace(/ü/g, 'u').replace(/Ü/g, 'U');

        return cleaned.toLowerCase();
    },
    normalizeString: (str) => {
        if (typeof str !== 'string') return '';
        return str
            .normalize("NFD") // Türkçe karakterleri ayır
            .replace(/[\u0300-\u036f]/g, "") // Üst karakterleri kaldır
            .replace(/[^a-zA-Z0-9]/g, "") // Özel karakterleri ve boşlukları kaldır
            .toLowerCase(); // Küçük harfe çevir
    },
    normalizeUrl: (url) => {
        if (typeof url !== 'string') return '';
        // ? işaretinden sonrasını kes
        const cleanUrl = url.split('?')[0];
        return fnSupport.normalizeString(cleanUrl);
    },

};

async function main() {
    const dtBop = Date.now();
    try {

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);


        async function getTableA() {
            const q_summary = `
                SELECT TESIS_ID, KATEGORI, TESIS_ADI, ZINCIR_ADI, ZINCIR_ADI_2, TESIS_YAPIM_YILI, TESIS_YENILEME_YILI, TESIS_KAYIT_TARIHI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, LATITUDE, LONGITUDE, TESIS_ONAY, DURUM, WEBSITE_LISTED, KIMLER_KALMALI, OTOMATIK_FIYATLANDIRMA, URL, SEO_URL, TESIS_KAPASITE, KONAKLAMA_ACIKLAMA, ACIKLAMA, ERKEK_KABUL_EDILMEZ, UYELIK_KATEGORI, TESIS_DETAY_ID, TESIS_UNVAN, ADRES1_DETAY, ADRES2, SEMT, SEHIR, TEL, KONTAK_AD, KONTAK_SOYAD, KONTAK_GOREV, KONTAK_EMAIL, KONTAK_TEL, KONTAK_DAHILI, PERSONEL_ACIKLAMA_ARTI, PERSONEL_ACIKLAMA_EKSI, PERSONEL_ACIKLAMA_NOT, ACIK_HAVUZ_BOYUTU, ACIK_HAVUZ_DERINLIGI, KAPALI_HAVUZ_BOYUTU, KAPALI_HAVUZ_DERINLIGI, DENIZ_OZELLIKLERI, KOMISYON, FIYATLANDIRMA_ACIKLAMA, T_ONCELIK, SOZLESME_ONAY, CALISMA_MODELI, ALINAN_ONLEMLER, OTEL_ISLETME_BELGE_TIPI, OTEL_ISLETME_NUMARASI, TARIH, TESIS_PUANI, GUNLUK_TESIS_PUANI, TP_TESIS_MIMARISI, TP_TEMIZLIK_HIJYEN, TP_YIYECEK_ICECEK, TP_ANIMASYON, TP_SERVIS_PERSONEL, BRUT_CIRO, BRUT_REZERVASYON_SAYISI, NET_CIRO, NET_REZERVASYON_SAYISI, IPTAL_SAYISI, BOLGE_NO, ALT_BOLGE_NO, FTGD_ID, Satislar, WebTrafik
                    FROM q_summary
                    where 1=1
                    --and BOLGE_ADI = 'Muğla' 
                    and ALT_BOLGE_ADI = '${desti}' 
                    and WEBSITE_LISTED = 'TRUE';
                `;

            const summaryResult = await db.all(q_summary);
            return summaryResult;
        }

        // Tablo B verilerini JSON dosyasından oku
        function getTableB() {
            // JSON dosyasının yolu
            const jsonPath = path.join(process.cwd(), destiJson);

            return new Promise((resolve, reject) => {

            fs.readFile(jsonPath, 'utf8', (err, data) => {
                if (err) {
                    return console.error('jolly_hotels.json dosyası okunamadı:', err.message);
                }
                try {
                    const jsonData = JSON.parse((data));
                    // console.log('jolly_hotels.json verisi başarıyla okundu.', jsonData.length, 'kayıt bulundu.');
                    resolve(jsonData);
                } catch (parseErr) {
                    console.error('JSON verisi parse edilemedi:', parseErr.message);
                    reject(false)
                }
            });

            });
        }

        // Karşılaştırma fonksiyonu
        function compareData(tableA, tableB) {
            // Güvenli kontrol
            if (!Array.isArray(tableA)) tableA = [];
            if (!Array.isArray(tableB)) tableB = [];

            const onlyInA = [];
            const onlyInB = [];
            const common = [];

            // Tablo A verilerini normalize edip eşleşme kontrolü için sakla
            const normalizedA = tableA.map(aRecord => {
                const normalizedName = fnSupport.normalizeString(aRecord?.TESIS_ADI || '');
                const normalizedUrl = fnSupport.normalizeUrl(aRecord?.SEO_URL || '');
                return {
                    original: aRecord,
                    normalizedKey: normalizedName + '_' + normalizedUrl
                };
            });

            // Tablo B verilerini normalize edip eşleşme kontrolü için sakla
            const normalizedB = tableB.map(bRecord => {
                const normalizedName = fnSupport.normalizeString(bRecord?.name || '');
                const normalizedUrl = fnSupport.normalizeUrl(bRecord?.dataUrl || '');
                return {
                    original: bRecord,
                    normalizedKey: normalizedName + '_' + normalizedUrl
                };
            });

            // Tablo A'dan her bir kayıt için eşleşme kontrolü
            normalizedA.forEach(a => {
                const match = normalizedB.find(b => b.normalizedKey === a.normalizedKey);
                if (match) {
                    common.push({ a: a.original, b: match.original });
                } else {
                    onlyInA.push(a.original);
                }
            });

            // Tablo B'den her bir kayıt için eşleşme kontrolü
            normalizedB.forEach(b => {
                const match = normalizedA.find(a => a.normalizedKey === b.normalizedKey);
                if (!match) {
                    onlyInB.push(b.original);
                }
            });

            // Sonuçları yazdır
            console.log("=== KARŞILAŞTIRMA SONUÇLARI ===");
            console.log("Tablo A'da olup Tablo B'de olmayan kayıtlar sayısı:", onlyInA.length);
            console.log(onlyInA);

            console.log("\nTablo B'de olup Tablo A'da olmayan kayıtlar sayısı:", onlyInB.length);
            console.log(onlyInB);

            console.log("\nOrtak olan kayıtlar sayısı:", common.length);
            // console.log(common);

            console.log("\n=== ÖZET ===");
            console.log(`Tablo A (Tatil Sepeti) kayıt sayısı: ${tableA.length}`);
            console.log(`Tablo B (Jolly) kayıt sayısı: ${tableB.length}`);
            console.log(`A'da olup B'de olmayan: ${onlyInA.length}`);
            console.log(`B'de olup A'da olmayan: ${onlyInB.length}`);
            console.log(`Ortak olan: ${common.length}`);
        }

        let tableA = (await getTableA());
        console.log('Tablo A verileri alındı:', 'kayıt bulundu.', tableA.length);

        let tableB = (await getTableB());
        console.log('Tablo B verileri alındı:', 'kayıt bulundu.', tableB.length);
        compareData(tableA, tableB);
        // getTableA((tableA) => {
        //     getTableB((tableB) => {
        //         
        //     });
        // });

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
