import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";

import dotenv from 'dotenv';
// .env dosyasını yükle
dotenv.config();

// const genAI = new GoogleGenerativeAI(API_KEY);
// const model = genAI.getGenerativeModel({ model: 'gemini-pro' });


const fnSupport = {
    normalizeJsonObject: (jsonObj) => {
        const normalizedObj = {};
        for (const key in jsonObj) {
            if (Object.prototype.hasOwnProperty.call(jsonObj, key)) {
                let value = jsonObj[key];
                // Convert null or undefined to empty string before normalization
                if (value === null || value === undefined) {
                    value = '';
                }
                normalizedObj[key] = typeof value === 'string' ? fnSupport.normalizeText(value) : value;
            }
        }
        return normalizedObj;
    },

  extractAndParseJSON: (responseText) => {
    try {
      // Markdown kod bloğu işaretlerini (`json` ve ``` işaretlerini) temizleyin
      const jsonRegex = /```json\s*([\s\S]*?)\s*```/; // JSON içeriğini bulmak için regex
      const match = responseText.match(jsonRegex);

      if (!match || !match[1]) {
        throw new Error("Yanıtta geçerli bir JSON bulunamadı.");
      }

      const jsonString = match[1].trim(); // JSON içeriğini al

      // JSON'ı parse edin
      const parsedJSON = JSON.parse(jsonString);
      return parsedJSON;
    } catch (error) {
      console.error("JSON extraction veya parsing hatası:", error.message);
      try {
        let jsonString = responseText;
        const parsedJSON = JSON.parse(jsonString);
        return parsedJSON;
      } catch (e) {
        console.error("JSON extraction veya parsing hatası:", e.message);
        // throw error;
        return responseText;
      }
    }
  },
    normalizeText: (text) => {
        if (!text || typeof text !== 'string') return '';
        let cleaned = text;
        // HTML etiketleri ve özel karakterler temizleniyor
        cleaned = cleaned
            .replace(/<[^>]*>/g, ' ')
            .replace(/&nbsp;/gi, ' ')
            .replace(/&amp;/gi, '&')
            .replace(/</gi, '<')
            .replace(/>/gi, '>')
            .replace(/&quot;/gi, '"')
            .replace(/&#39;/gi, "'")
            .replace(/&apos;/gi, "'")
            .replace(/&[a-zA-Z0-9#]+;/g, ' ')
            .replace(/[\t\n\r]/g, ' ')
            .replace(/[\u0000-\u001F\u007F]/g, '')
            .replace(/\s+/g, ' ');

        // Türkçe karakter dönüşümü
        cleaned = cleaned
            .replace(/ç/g, 'c').replace(/Ç/g, 'C')
            .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
            .replace(/ı/g, 'i').replace(/İ/g, 'I')
            .replace(/ö/g, 'o').replace(/Ö/g, 'O')
            .replace(/ş/g, 's').replace(/Ş/g, 'S')
            .replace(/ü/g, 'u').replace(/Ü/g, 'U');

        return cleaned.toLowerCase();
    },
    normalizeString: (str) => {
        if (typeof str !== 'string') return '';
        return str
            .normalize("NFD") // Türkçe karakterleri ayır
            .replace(/[\u0300-\u036f]/g, "") // Üst karakterleri kaldır
            .replace(/[^a-zA-Z0-9]/g, "") // Özel karakterleri ve boşlukları kaldır
            .toLowerCase(); // Küçük harfe çevir
    },
    normalizeUrl: (url) => {
        if (typeof url !== 'string') return '';
        // ? işaretinden sonrasını kes
        const cleanUrl = url.split('?')[0];
        return fnSupport.normalizeString(cleanUrl);
    },

};
// jolly_hotels_bodrum-otelleri_2025.07.28_2025.08.04
const destiTR = 'Antalya'
const desti = fnSupport.normalizeString(destiTR);
const destiJson = `jolly_hotels_${desti}-otelleri_2025.07.28_2025.08.04.json`;

const modelQueries = {
  ollama: {
    vectorEmbeddings: async ({ text, uri, model, }) => {
      uri = uri || 'http://127.0.0.1:11434/api/embeddings';
      model = model || "nomic-embed-text:latest";

      if (!text) {
        console.error('modelQueries - ollama: Error no text', text);
        return null;
      }

      try {
        const response = await fetch(uri, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model,
            prompt: text,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('modelQueries - Ollama embedding error:', errorData);
          return null;
        }

        const data = await response.json();
        return data.embedding;
      } catch (error) {
        console.error('Error generating embedding with modelQueries - Ollama:', error);
        return null;
      }
    },
    query: async ({ model, prompt, stream = false }) => {
      const uri = 'http://127.0.0.1:11434/api/generate';
      model = model || "qwen3:8b"; // Specified model gemma3:1b deepseek-r1:8b
      if (!prompt) {
        console.error('modelQueries - ollama: Error no prompt', prompt);
        return null;
      }
      try {
        const response = await fetch(uri, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model,
            prompt,
            stream, // We want a single response
          }),
        });
        if (!response.ok) {
          const errorData = await response.json();
          console.error('modelQueries Ollama query error:', errorData);
          return null;
        }
        const data = await response.json();
        const modelResponse = data.response;
        return modelResponse;
      } catch (error) {
        console.error('Error fetching metadata from Ollama:', error);
        return null;
      }
    },
  },
  google_gemini: {
    query: async ({ model, prompt, stream = false }) => { 
      // Initialize Google AI
      const modelName = model || 'gemini-2.5-flash-lite-preview-06-17'; // Specify the model you want to use
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
      const genAIModel = genAI.getGenerativeModel({ model: modelName });
      const result = await genAIModel.generateContent(prompt);
      const response = result.response.text().trim();
      return response;
    },
  },
  prompts: {
    metadataExtraction: async (queryText, inputLang = 'auto') => {
        return `
        You are a hotel information parser. Extract the following structured metadata from the given query text about a hotel or accommodation facility.

        FIELDS TO EXTRACT:
        1. **country**: The country where the hotel is located (e.g., Turkey, France). Only extract if explicitly mentioned.
        2. **region**: The region or area within the country (e.g., Aegean Region, Cappadocia). Only extract if explicitly mentioned.
        3. **city**: The city where the hotel is located (e.g., Istanbul, Antalya). Only extract if explicitly mentioned.
        3. **town**: The town or district within the city (e.g., Kadiköy, Belek). Only extract if explicitly mentioned.
        4. **hotelName**: The exact name of the hotel or facility as written in the text. Do NOT paraphrase or guess.
        5. **category**: The category or star rating of the hotel (e.g., (KATEGORI) VALUES
          ('5 Yıldızlı Otel'), ('4 Yıldızlı Otel'), ('Tatil Köyü'), ('3 Yıldızlı Otel'), ('(Yok)'), ('Özel Belgeli'), ('Hotel'),
          ('Otel'), ('Butik Otel'), ('2 Yıldızlı Otel'), ('1 Yıldızlı Hotel'), ('1 Yıldızlı Otel'), ('Motel'), ('Apart'), ('Oberj'), ('Pansiyon'), 
          ('2 Yıldızlı Hotel'), ('Dağ Evi'), ('4 Yıldızlı Hotel'), ('5 Yıldızlı Hotel'), ('3 Yıldızlı Hotel'), ('Kırsal Turizm Tesisi'), ('Villa'), ('Camping'), ('Hostel'), 
          ('Köy Evi'), ('1.Sınıf'), ('Plaj');
        ). Only extract if explicitly mentioned.

        INSTRUCTIONS:
        - Return only a valid JSON object with exactly these keys.
        - Do NOT add explanations, markdown, or extra text.
        - If any field cannot be determined from the text, set its value to null.
        - Use Turkish for values if the input text is in Turkish; otherwise, use English. Keys remain in English.
        - Never make up or infer hotel names. Only use what is explicitly stated in the text.
        - Be strict and only extract values that clearly match the criteria.

        TEXT TO ANALYZE:
        "${queryText}"
        `;
    },
    matchPrompt: () => {
      return `
      You are given two lists of hotel names:
        - List A: Names from a local Turkish database.
        - List B: Names from a booking platform (may be in Turkish or English).

        Your task is to:
        1. Identify hotels in **List B that do NOT have a matching name in List A** (noMatches).
        2. Identify hotels in **List B that have a similar name in List A but are NOT a match** (similarButNoMatch), including the reason.

        Return your response strictly in the following JSON format:

        {
        "noMatches": [
            {
            "hotelB": "Name from List B",
            "reason": "Brief explanation"
            }
        ],
        "similarButNoMatch": [
            {
            "hotelA": "Name from List A",
            "hotelB": "Name from List B",
            "reason": "Brief explanation"
            }
        ]
        }

        Compare the names carefully, taking into account:
        - Different languages (Turkish vs English)
        - Spelling variations
        - Use of acronyms or abbreviations (e.g., "Golden Age Hotel" vs "Golden Age")
        - Extra descriptors (e.g., "Hotel", "Resort", "Suites")
        - Common synonyms or translations
        - Special characters or accents

        List A (Local DB): ["Golden Age Hotel Yalıkavak", "Kılıç Otel Bodrum", "Mardan Palace Antalya"]
        List B (Booking Platform): ["Golden Age Hotel", "Kilic Hotel Bodrum", "Sunset Resort & Spa", "Seashine Hotel"]
      `;
    }
  }
};

async function main() {
    const dtBop = Date.now();

    const modelName = 'gemini-2.5-flash-lite-preview-06-17'; // Specify the model you want to use
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
    const model = genAI.getGenerativeModel({ model: modelName });

    try {

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);


        async function getTableA() {
            const q_summary = `
                SELECT TESIS_ID, KATEGORI, TESIS_ADI, ZINCIR_ADI, ZINCIR_ADI_2, TESIS_YAPIM_YILI, TESIS_YENILEME_YILI, TESIS_KAYIT_TARIHI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, LATITUDE, LONGITUDE, TESIS_ONAY, DURUM, WEBSITE_LISTED, OTOMATIK_FIYATLANDIRMA, URL, SEO_URL, TESIS_KAPASITE, ERKEK_KABUL_EDILMEZ, UYELIK_KATEGORI, TESIS_DETAY_ID, TESIS_UNVAN, ADRES1_DETAY, ADRES2, SEMT, SEHIR, TEL, KONTAK_AD, KONTAK_SOYAD, KONTAK_GOREV, KONTAK_EMAIL, KONTAK_TEL, KONTAK_DAHILI, PERSONEL_ACIKLAMA_ARTI, PERSONEL_ACIKLAMA_EKSI, PERSONEL_ACIKLAMA_NOT, ACIK_HAVUZ_BOYUTU, ACIK_HAVUZ_DERINLIGI, KAPALI_HAVUZ_BOYUTU, KAPALI_HAVUZ_DERINLIGI, DENIZ_OZELLIKLERI, KOMISYON, FIYATLANDIRMA_ACIKLAMA, T_ONCELIK, SOZLESME_ONAY, CALISMA_MODELI, ALINAN_ONLEMLER, OTEL_ISLETME_BELGE_TIPI, OTEL_ISLETME_NUMARASI, TARIH, TESIS_PUANI, GUNLUK_TESIS_PUANI, TP_TESIS_MIMARISI, TP_TEMIZLIK_HIJYEN, TP_YIYECEK_ICECEK, TP_ANIMASYON, TP_SERVIS_PERSONEL, BRUT_CIRO, BRUT_REZERVASYON_SAYISI, NET_CIRO, NET_REZERVASYON_SAYISI, IPTAL_SAYISI, BOLGE_NO, ALT_BOLGE_NO, FTGD_ID
                    --, Satislar, WebTrafik, KIMLER_KALMALI, , KONAKLAMA_ACIKLAMA, ACIKLAMA
                    FROM q_summary
                    where 1=1
                    and BOLGE_ADI = '${destiTR}'  
                    --and ALT_BOLGE_ADI = '${destiTR}' 
                    and WEBSITE_LISTED = 'TRUE';
                `;

            const summaryResult = await db.all(q_summary);
            if (!summaryResult || summaryResult.length === 0) {
                console.error(`💥 q_summary tablosunda kayıt bulunamadı. Lütfen veri tabanını kontrol edin ve tekrar deneyin. ${q_summary}`);
                process.exit(1);
            } else {
                console.log(`✓ q_summary tablosunda ${summaryResult.length} kayıt bulundu.`);
            }   
            return summaryResult;
        }

        // Tablo B verilerini JSON dosyasından oku
        function getTableB() {
            // JSON dosyasının yolu
            const jsonPath = path.join(process.cwd(), destiJson);

            return new Promise((resolve, reject) => {

            fs.readFile(jsonPath, 'utf8', (err, data) => {
                if (err) {
                    return console.error('jolly_hotels.json dosyası okunamadı:', err.message);
                }
                try {
                    const jsonData = JSON.parse((data));
                    // console.log('jolly_hotels.json verisi başarıyla okundu.', jsonData.length, 'kayıt bulundu.');
                    resolve(jsonData);
                } catch (parseErr) {
                    console.error('JSON verisi parse edilemedi:', parseErr.message);
                    reject(false)
                }
            });

            });
        }

        async function compareHotelLists(listA, listB) {
            return new Promise(async (resolve, reject) => {
                const prompt = `
                    You are given two lists of hotel names:
                    - List A: Names from a local Turkish database.
                    - List B: Names from a booking platform (may be in Turkish or English).

                    Your task is to:
                    1. Identify hotels in **List B that do NOT have a matching name in List A** (noMatches).
                    2. Identify hotels in **List B that have a similar name in List A but are NOT a match** (similarButNoMatch), including the reason.

                    Return your response strictly in the following JSON format:

                    {
                    "noMatches": [
                        {
                        "hotelB": "Name from List B",
                        "reason": "Brief explanation"
                        }
                    ],
                    "similarButNoMatch": [
                        {
                        "hotelA": "Name from List A",
                        "hotelB": "Name from List B",
                        "reason": "Brief explanation"
                        }
                    ]
                    }

                    Compare the names carefully, taking into account:
                    - Different languages (Turkish vs English)
                    - Spelling variations
                    - Use of acronyms or abbreviations (e.g., "Golden Age Hotel" vs "Golden Age")
                    - Extra descriptors (e.g., "Hotel", "Resort", "Suites")
                    - Common synonyms or translations
                    - Special characters or accents

                    List A (Local DB): [${listA.map(n => `"${n.replace(/"/g, '\\"')}"`).join(', ')}]
                    List B (Booking Platform): [${listB.map(n => `"${n.replace(/"/g, '\\"')}"`).join(', ')}]
                `;

            // console.log('llm prompt:', prompt);

            try {
                // let fnModel = modelQueries.ollama.query; // modelQueries.google_gemini.query;
                let fnModel = modelQueries.google_gemini.query;
                const result = await fnModel({prompt});
                const response = result;

                //response u localde gecici olarak txt olarak kayit et
                try {
                    const outputFile = path.join(process.cwd(), `llm-response-${desti}.txt`);
                    await fs.writeFileSync(outputFile, response, 'utf8');
                    console.log(`LLM yanıtı "${outputFile}" dosyasına kaydedildi.`);
                } catch (writeErr) {
                    console.error('LLM yanıtı dosyaya yazılamadı:', writeErr.message);
                }

                let jsonOutput;
                try {
                    jsonOutput = fnSupport.extractAndParseJSON(response);
                } catch (e) {
                    console.error('LLM yanıtı JSON olarak parse edilemedi:', e.message);
                    resolve ({
                        noMatches: [],
                        similarButNoMatch: [],
                        error: 'LLM format hatası',
                        rawResponse: response
                    }) ;
                }
                resolve(jsonOutput)
            } catch (error) {
                console.error('LLM isteğinde hata:', error.message);
                resolve({
                    noMatches: [],
                    similarButNoMatch: [],
                    error: 'API hatası'
                })
            }

            });
        }

        // Karşılaştırma fonksiyonu
        async function compareData(tableA, tableB) {
            // Güvenli kontrol
            if (!Array.isArray(tableA)) tableA = [];
            if (!Array.isArray(tableB)) tableB = [];

            const onlyInA = [];
            const onlyInB = [];
            const common = [];

            // Tablo A verilerini normalize edip eşleşme kontrolü için sakla
            const normalizedA = tableA.map(aRecord => {
                const normalizedName = fnSupport.normalizeString(aRecord?.TESIS_ADI || '');
                const normalizedUrl = ''; // fnSupport.normalizeUrl(aRecord?.SEO_URL || '');
                return {
                    original: aRecord,
                    normalizedKey: normalizedName + '_' + normalizedUrl
                };
            });

            // Tablo B verilerini normalize edip eşleşme kontrolü için sakla
            const normalizedB = tableB.map(bRecord => {
                const normalizedName = fnSupport.normalizeString(bRecord?.name || '');
                const normalizedUrl =''; //  fnSupport.normalizeUrl(bRecord?.dataUrl || '');
                return {
                    original: bRecord,
                    normalizedKey: normalizedName + '_' + normalizedUrl
                };
            });

            // Tablo A'dan her bir kayıt için eşleşme kontrolü
            normalizedA.forEach(a => {
                const match = normalizedB.find(b => b.normalizedKey === a.normalizedKey);
                if (match) {
                    common.push({ a: a.original, b: match.original });
                } else {
                    onlyInA.push(a.original);
                }
            });

            // Tablo B'den her bir kayıt için eşleşme kontrolü
            normalizedB.forEach(b => {
                const match = normalizedA.find(a => a.normalizedKey === b.normalizedKey);
                if (!match) {
                    onlyInB.push(b.original);
                }
            });

            // Sonuçları yazdır
            console.log("=== KARŞILAŞTIRMA SONUÇLARI ===");
            // console.log("Tablo A'da olup Tablo B'de olmayan kayıtlar sayısı:", onlyInA.length);
            // console.log(onlyInA);
            // bunlari dosyaya kaydet
            const outputFileA = path.join(process.cwd(), `scape-compare-${desti}_sadeceTatilSepeti.json`);
            fs.writeFileSync(outputFileA, JSON.stringify(onlyInA, null, 2));
            // console.log(`Tablo A'da olup Tablo B'de olmayan kayıtlar "${outputFileA}" dosyasına kaydedildi.`);

            // console.log("\nTablo B'de olup Tablo A'da olmayan kayıtlar sayısı:", onlyInB.length);
            // console.log(onlyInB);
            // bunlari dosyaya kaydet
            const outputFileB = path.join(process.cwd(), `scape-compare-${desti}_sadeceJolly.json`);
            fs.writeFileSync(outputFileB, JSON.stringify(onlyInB, null, 2));
            // console.log(`Tablo B'de olup Tablo A'da olmayan kayıtlar "${outputFileB}" dosyasına kaydedildi.`);

            // console.log("\nOrtak olan kayıtlar sayısı:", common.length);
            const outputFileC = path.join(process.cwd(), `scape-compare-${desti}_ortak.json`);
            fs.writeFileSync(outputFileC, JSON.stringify(common, null, 2));
            // console.log(`Ortak olan kayıtlar "${outputFileC}" dosyasına kaydedildi.`);
            // console.log(common);
            // console.log("\n=== ÖZET ===");
            console.log(`Tablo A (Tatil Sepeti) kayıt sayısı: ${tableA.length}`);
            console.log(`Tablo B (Jolly) kayıt sayısı: ${tableB.length}`);
            console.log(`A'da olup B'de olmayan: ${onlyInA.length}`);
            console.log(`B'de olup A'da olmayan: ${onlyInB.length}`);
            console.log(`Ortak olan: ${common.length}`);

            const dtLLM = Date.now();
            // LLM ile karşılaştırma yap
            const listA = tableA.map(a => a.TESIS_ADI || '').filter(Boolean);
            const listB = onlyInB.map(b => b.name || '').filter(Boolean);
            const comparisonResult = await compareHotelLists(listA, listB);
            // console.log('=== LLM KARŞILAŞTIRMA SONUÇLARI ===');
            // console.log(comparisonResult);
            const outputFileCC = path.join(process.cwd(), `scape-compare-${desti}_llmcomparison.json`);
            fs.writeFileSync(outputFileCC, JSON.stringify(comparisonResult, null, 2));
            const dtEnd = Date.now();
            console.log('LLM isteği tamamlandı:', dtEnd - dtLLM, 'ms');

            //veritabanına final notları ekle.
            await updateDatabaseWithComparisons(db, common, comparisonResult);

        }

        // Veritabanı güncelleme fonksiyonu
        async function updateDatabaseWithComparisons(db, common, comparisonResult) {
            // try {
            //     // Önce yeni kolonları ekle
            //     await db.exec(`
            //         ALTER TABLE scaped_hotels
            //         ADD COLUMN TABLO_KARSILASTIRMA TEXT;
            //     `);
            //     console.log('✓ TABLO_KARSILASTIRMA kolonu eklendi.');
            // } catch (error) {
            //     if (error.message.includes('duplicate column name')) {
            //         console.log('✓ TABLO_KARSILASTIRMA kolonu zaten mevcut.');
            //     } else {
            //         console.error('TABLO_KARSILASTIRMA kolonu eklenirken hata:', error.message);
            //     }
            // }

            // try {
            //     await db.exec(`
            //         ALTER TABLE scaped_hotels
            //         ADD COLUMN KARSILASTIRMA_NOTLAR TEXT;
            //     `);
            //     console.log('✓ KARSILASTIRMA_NOTLAR kolonu eklendi.');
            // } catch (error) {
            //     if (error.message.includes('duplicate column name')) {
            //         console.log('✓ KARSILASTIRMA_NOTLAR kolonu zaten mevcut.');
            //     } else {
            //         console.error('KARSILASTIRMA_NOTLAR kolonu eklenirken hata:', error.message);
            //     }
            // }

            // Max process_id değerini bul
            const maxProcessIdResult = await db.get(`
                SELECT MAX(process_id) as max_process_id FROM scaped_hotels
            `);
            const maxProcessId = maxProcessIdResult?.max_process_id;

            if (!maxProcessId) {
                console.log('⚠️ Veritabanında process_id bulunamadı.');
                return;
            }

            console.log(`✓ Max process_id: ${maxProcessId}`);

            // Max process_id'ye eşit olan kayıtları bul (Jolly'deki kayıtlar)
            const jollyRecords = await db.all(`
                SELECT id, name FROM scaped_hotels
                WHERE process_id = ?
            `, [maxProcessId]);

            console.log(`✓ Jolly kayıtları bulundu: ${jollyRecords.length} adet`);

            // Common dizinindeki b anahtarına sahip olan name'lerden eşleşenleri güncelle
            let commonUpdated = 0;
            for (const commonItem of common) {
                const hotelName = commonItem.b?.name;
                if (hotelName) {
                    const result = await db.run(`
                        UPDATE scaped_hotels
                        SET TABLO_KARSILASTIRMA = 'TTS de var - ortak'
                        WHERE process_id = ? AND name = ?
                    `, [maxProcessId, hotelName]);

                    if (result.changes > 0) {
                        commonUpdated++;
                    }
                }
            }
            console.log(`✓ Ortak kayıtlar güncellendi: ${commonUpdated} adet`);

            // ComparisonResult'daki hotelB ile eşleştirerek LLM karşılaştırma sonuçlarını güncelle
            let llmUpdated = 0;

            // noMatches dizinini işle
            if (comparisonResult.noMatches && Array.isArray(comparisonResult.noMatches)) {
                for (const noMatch of comparisonResult.noMatches) {
                    const hotelName = noMatch.hotelB;
                    const reason = noMatch.reason;

                    if (hotelName) {
                        const comparisonText = 'LLM Comparison - noMatches';
                        const result = await db.run(`
                            UPDATE scaped_hotels
                            SET TABLO_KARSILASTIRMA = ?,
                                KARSILASTIRMA_NOTLAR = ?
                            WHERE process_id = ? AND name = ?
                        `, [comparisonText, reason, maxProcessId, hotelName]);

                        if (result.changes > 0) {
                            llmUpdated++;
                        }
                    }
                }
            }

            // similarButNoMatch dizinini işle
            if (comparisonResult.similarButNoMatch && Array.isArray(comparisonResult.similarButNoMatch)) {
                for (const similarMatch of comparisonResult.similarButNoMatch) {
                    const hotelName = similarMatch.hotelB;
                    const reason = similarMatch.reason;

                    if (hotelName) {
                        const comparisonText = 'LLM Comparison - similarButNoMatch';
                        const result = await db.run(`
                            UPDATE scaped_hotels
                            SET TABLO_KARSILASTIRMA = ?,
                                KARSILASTIRMA_NOTLAR = ?
                            WHERE process_id = ? AND name = ?
                        `, [comparisonText, reason, maxProcessId, hotelName]);

                        if (result.changes > 0) {
                            llmUpdated++;
                        }
                    }
                }
            }

            console.log(`✓ LLM karşılaştırma sonuçları güncellendi: ${llmUpdated} adet`);

            // Güncelleme sonuçlarını kontrol et
            const updatedRecords = await db.all(`
                SELECT name, TABLO_KARSILASTIRMA, KARSILASTIRMA_NOTLAR
                FROM scaped_hotels
                WHERE process_id = ? AND (TABLO_KARSILASTIRMA IS NOT NULL OR KARSILASTIRMA_NOTLAR IS NOT NULL)
                LIMIT 10
            `, [maxProcessId]);

            console.log('✓ Güncellenen kayıt örnekleri:');
            updatedRecords.forEach(record => {
                console.log(`  - ${record.name}: ${record.TABLO_KARSILASTIRMA} | ${record.KARSILASTIRMA_NOTLAR}`);
            });
        }

        let tableA = (await getTableA());
        console.log('Tablo A verileri alındı:', 'kayıt bulundu.', tableA.length);
        let tableB = (await getTableB());
        console.log('Tablo B verileri alındı:', 'kayıt bulundu.', tableB.length);
        compareData(tableA, tableB);
        // getTableA((tableA) => {
        //     getTableB((tableB) => {
        //         
        //     });
        // });

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
