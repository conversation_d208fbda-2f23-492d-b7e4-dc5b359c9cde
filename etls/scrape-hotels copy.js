const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

(async () => {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
    });
    const page = await context.newPage();

    // Tarihleri oluştur
    const today = new Date();
    let startDate = new Date(today);
    startDate.setDate(startDate.getDate() + 7);
    let endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 7);
    const dest = 'cesme-otelleri';
    const formatDate = (date) => date.toISOString().split('T')[0].replace(/-/g, '.');
    const Filters = `?Filters=StartDate:${formatDate(startDate)};EndDate:${formatDate(endDate)}`; //'';
    const url = `https://www.jollytur.com/${dest}${Filters}`;
    console.log(`Navigating to: ${url}`);
    const fileName = `jolly_hotels_${dest}_${formatDate(startDate)}_${formatDate(endDate)}.json`;

    // Yanıt klasörü
    //   const responseDir = path.join(__dirname, 'api_responses');
    //   if (!fs.existsSync(responseDir)) fs.mkdirSync(responseDir);

    let responseCount = 0;

    let allHotels = [];
    // ✅ route tanımlaması burada ve doğru URL ile
    await page.route('https://www.jollytur.com/hotel/GetListPagePartials', async route => {
        const request = route.request();
        let postData = request.postData();
        let newPostData = postData;

        if (postData && postData.includes('Location=')) {
            const params = new URLSearchParams(postData);
            const filters = params.get('Filters');
            if (filters) {
                const newFilters = filters.split(';').filter(param => !param.startsWith('Location=')).join(';');
                params.set('Filters', newFilters);
                newPostData = params.toString();
                console.log(`Modified API request postData: ${newPostData}`);
            }
        }

        const headers = request.headers();
        delete headers['content-length']; // Playwright will recalculate this automatically

        route.continue({
            postData: newPostData,
            headers: headers,
        });
    });

    await page.route('https://www.jollytur.com/bodrum-otelleri*', async route => {
        const requestUrl = new URL(route.request().url());
        const filtersParam = requestUrl.searchParams.get('Filters');

        if (filtersParam && filtersParam.includes('Location=')) {
            const newFilters = filtersParam.split(';').filter(param => !param.startsWith('Location=')).join(';');
            requestUrl.searchParams.set('Filters', newFilters);
            console.log(`Modified main page URL: ${requestUrl.toString()}`);
            route.continue({ url: requestUrl.toString() });
            return;
        }
        route.continue();
    });

    // Sayfayı yükle
    try {
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
        console.log('Sayfa başarıyla yüklendi');
        console.log(`Current page URL: ${page.url()}`);
        await page.waitForTimeout(10000);
    } catch (error) {
        console.error('Sayfa yüklenirken hata:', error);
        throw error;
    }

    // "Daha fazla tesis göster" butonuna tıkla
    let moreButtonVisible = true;
    let clickCount = 0;
    let initialHotelCount = 0;
    let currentPage = 0;

    // Initial hotel count
    try {
        const countElement = await page.$('#selectCountbox');
        if (countElement) {
            initialHotelCount = parseInt(await countElement.innerText(), 10);
            console.log(`Initial hotel count: ${initialHotelCount}`);
        }
    } catch (error) {
        console.error('Error getting initial hotel count:', error);
    }

    while (moreButtonVisible) {
        currentPage++;
        // Check current hotel count
        try {
            const currentCountElement = await page.$('#selectCountbox');
            if (currentCountElement) {
                const currentHotelCount = parseInt(await currentCountElement.innerText(), 10);
                if (initialHotelCount !== 0 && currentHotelCount !== initialHotelCount) {
                    console.log(`Hotel count changed from ${initialHotelCount} to ${currentHotelCount} on page ${currentPage}. Breaking loop.`);
                    break;
                }
            }
        } catch (error) {
            console.error('Error getting current hotel count:', error);
        }

        const moreButton = page.locator('button.button.third:has-text("Daha fazla tesis göster")');
        try {
            // "Tamamını görüntülediniz" yazısının görünüp görünmediğini kontrol et
            const isAllLoaded = await page.$eval('.moreTextList', el =>
                el.innerText.toLowerCase().includes('tamamını görüntülediniz')
            );

            if (isAllLoaded) {
                console.log('"Tamamını görüntülediniz" yazısı göründü. Döngüden çıkılıyor.');
                break;
            }

            const exists = await moreButton.count() > 0;
            if (!exists) break;

            // data-pageno bilgisini al
            const dataPageNo = await moreButton.getAttribute('data-pageno');

            await moreButton.scrollIntoViewIfNeeded({ timeout: 9000 });
            await moreButton.click({ timeout: 14000 });
            clickCount++;
            console.log(`Butona ${clickCount}. kez tıklandı, data-pageno: ${dataPageNo}`);

            await page.waitForTimeout(8000);
        } catch (e) {
            console.log('Hata veya buton sona erdi:', e.message);
            moreButtonVisible = false;
        }
    }

    // Otelleri çek
    try {
        const hotels = await page.$$eval('.tour-list-area .list', cards => {
            return cards.map(card => {
                const titleElement = card.querySelector('.title');
                const locationElement = card.querySelector('.tour-route-map');
                const imageElement = card.querySelector('.product-card-image-container img');
                const oldPriceElement = card.querySelector('.old-price');
                const currentPriceElement = card.querySelector('.current-price');

                return {
                    name: titleElement?.innerText.trim() || null,
                    location: locationElement?.innerText.trim().replace('(Haritada Göster)', '').trim() || null,
                    lat: card.querySelector('.show-map')?.getAttribute('data-lat') || null,
                    long: card.querySelector('.show-map')?.getAttribute('data-long') || null,
                    imageUrl: (imageElement?.getAttribute('data-src') || imageElement?.getAttribute('src'))?.trim() || null,
                    oldPrice: oldPriceElement?.innerText.trim().replace(/\s+/g, '') || null,
                    currentPrice: currentPriceElement?.innerText.trim().replace(/\s+/g, '').replace('TL', '') + ' TL' || null,
                    // card: card.outerHTML.trim() || null,

                    // Yeni eklenen data-* attributelar
                    dataUrl: card.getAttribute('data-url'),
                    masterHotelId: card.getAttribute('data-masterhotelid'),
                    posKey: card.getAttribute('data-poskey'),
                    productCode: card.getAttribute('data-productcode'),
                    discountedPrice: card.getAttribute('data-discountedprice'),
                    totalPrice: card.getAttribute('data-totalprice'),
                    currency: card.getAttribute('data-currency'),
                    useCancelWarranty: card.getAttribute('data-usecancelwarranty'),
                    isDomestic: card.getAttribute('data-isdomestic'),
                    hotelCategoryId: card.getAttribute('data-hotelcategoryid')

                };
            });
        });

        console.log(`${hotels.length} otel başarıyla çekildi`);

        // let hotelsstg = hotels.filter(hotel => hotel.name);

        // Tekil otelleri al (duplicate isimleri temizle)
        const uniqueHotels = [];
        const hotelNames = new Set();

        for (const hotel of hotels) {
            if (hotel.name && !hotelNames.has(hotel.name)) {
                hotelNames.add(hotel.name);
                uniqueHotels.push(hotel);
            } else {
                console.log(`Otel ismi eksik veya zaten var: ${hotel.name}`);
            }
        }

        console.log(`${uniqueHotels.length} benzersiz otel bulundu`);


        const filePath = path.join(__dirname, fileName);
        fs.writeFileSync(filePath, JSON.stringify(uniqueHotels, null, 2), 'utf8');
        console.log(`Otel verisi kaydedildi: ${filePath}`);


        const filePathAll = path.join(__dirname, 'jolly_hotels_all.json');
        fs.writeFileSync(filePathAll, JSON.stringify(allHotels, null, 2), 'utf8');
        console.log(`All Otel verisi kaydedildi: ${filePathAll}`);


        // Her API yanıtı için eşleştirme yap
        const mergedAllResponses = [];

        allHotels.forEach(response => {
            const merged = mergedList(response, uniqueHotels);
            mergedAllResponses.push(...merged); // tüm eşleştirilmiş otelleri tek diziye ekle
        });

        // Tekilleştirme (isim bazlı)
        const seenNames = new Set();
        const mergedUniqueHotels = mergedAllResponses.filter(hotel => {
            const normalizedName = hotel.item_name?.toLowerCase().trim();
            if (!normalizedName || seenNames.has(normalizedName)) return false;
            seenNames.add(normalizedName);
            return true;
        });

        // Sonuçları yaz
        const filePathAllM = path.join(__dirname, 'jolly_hotels_all_merged.json');
        fs.writeFileSync(filePathAllM, JSON.stringify(mergedUniqueHotels, null, 2), 'utf8');
        console.log(`Birleştirilmiş otel verisi kaydedildi: ${filePathAllM}`);



    } catch (error) {
        console.error('Otel verisi çekilirken hata:', error);
        const pageContent = await page.content();
        const debugPath = path.join(__dirname, 'debug_page.html');
        fs.writeFileSync(debugPath, pageContent, 'utf8');
        console.log(`Sayfa içeriği debug için kaydedildi: ${debugPath}`);
    }

    await context.close();
    await browser.close();
})();

const mergedList = (response, parsedData) => {
    const dataLayer = response.dataLayer || [];
    const analyticsDataList = response.analyticsDataList || [];

    // productId'ye göre hızlı erişim için Map yapısı
    const analyticsMap = new Map(
        analyticsDataList
            .filter(item => item.productId)
            .map(item => [item.productId.toString(), item])
    );

    // parsedData’yı masterHotelId’ye göre hızlı erişim için Map yapısına al
    const parsedMap = new Map();
    parsedData.forEach(item => {
        if (item.masterHotelId) {
            parsedMap.set(item.masterHotelId.toString(), item);
        }
    });


    // Eşleştirme işlemi
    const merged = [];

    dataLayer.forEach(item => {
        const productId = item.item_id?.toString();
        const matched = productId && analyticsMap.get(productId);
        const matchedParsed = productId && parsedMap.get(productId);

        if (matched) {
            merged.push({
                ...item,
                ...matched,
                parsedData: matchedParsed || null,
            });
        }
    });

    return merged;
};

const mergedList_ = (jsonData, parsedData) => {
    // Eşleştirme işlemi
    const dataLayer = jsonData.dataLayer || [];
    const analyticsDataList = jsonData.analyticsDataList || [];

    // analyticsDataList’i productId'ye göre Map yapısı ile hızlı erişime hazırla
    const analyticsMap = new Map();
    analyticsDataList.forEach(item => {
        if (item.productId) {
            analyticsMap.set(item.productId.toString(), item);
        }
    });

    // dataLayer’ı eşleştirerek tekil obje listesi oluştur
    const mergedList = [];

    dataLayer.forEach(item => {
        const productId = item.item_id?.toString();
        const matched = productId && analyticsMap.get(productId);

        if (matched) {
            mergedList.push({
                ...item,
                ...matched
            });
        }
    });

    // Benzersiz otel listesi oluştur (item_name'e göre)
    const uniqueHotels = [];
    const seenNames = new Set();

    for (const hotel of mergedList) {
        const normalizedName = hotel.item_name?.toLowerCase().trim();
        if (normalizedName && !seenNames.has(normalizedName)) {
            seenNames.add(normalizedName);
            uniqueHotels.push(hotel);
        }
    }
    return uniqueHotels;
}