import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

async function main() {
    const dtBop = Date.now();
    try {

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        //scaped_hotels_base tablosu var mı kontrol et
        const tableExists = await db.get(`
            SELECT name FROM sqlite_master WHERE type='table' AND name='scaped_hotels_base';
        `);
        if (!tableExists) {
            console.log('✓ scaped_hotels_base tablosu mevcut değil, oluşturuluyor...');
            // compare islemi icin base tabloyu olustur yok ise; 
            // await db.exec(`
            //     CREATE TABLE IF NOT EXISTS scaped_hotels_base (
            //         id INTEGER PRIMARY KEY AUTOINCREMENT,
            //         KATEGORI TEXT, 
            //         TESIS_ADI TEXT, 
            //         BOLGE_ADI TEXT, 
            //         ALT_BOLGE_ADI TEXT, 
            //         BOLGE_DETAY TEXT, 
            //         LATITUDE REAL, 
            //         LONGITUDE REAL, 
            //         WEBSITE_LISTED TEXT, 
            //         URL TEXT, 
            //         SEO_URL TEXT, 
            //         OTEL_ISLETME_NUMARASI TEXT,
            //         TTS_TESIS_ID TEXT, 
            //     );
            // `);
            console.log('✓ scaped_hotels_base tablosu oluşturuldu veya zaten mevcut.');

            // q_tesis_listesi tablosundan verileri al ve scaped_hotels_base tablosuna ekle
            // const q_summary = `
            //     SELECT 
            //         TTS_TESIS_ID, KATEGORI, TESIS_ADI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, 
            //         LATITUDE, LONGITUDE, WEBSITE_LISTED, URL, SEO_URL, OTEL_ISLETME_NUMARASI
            //     FROM q_tesis_listesi
            //     WHERE 1=1
            //     --and BOLGE_ADI = 'İzmir' 
            //     --AND ALT_BOLGE_ADI = 'Çeşme' 
            //     AND WEBSITE_LISTED = 'TRUE'
            //     LIMIT 100;
            // `;
            // const summaryResult = await db.all(q_summary);
            // if (!summaryResult || summaryResult.length === 0) {
            //     console.error(`💥 q_tesis_listesi tablosunda kayıt bulunamadı. Lütfen veri tabanını kontrol edin ve tekrar deneyin. ${q_summary}`);
            //     process.exit(1);
            // } else {
            //     console.log(`✓ q_tesis_listesi tablosunda ${summaryResult.length} kayıt bulundu.`);
            // }
            // Verileri scaped_hotels_base tablosuna ekle
            // const insertPromises = summaryResult.map(row => {
            //     const insertQuery = `
            //         INSERT INTO scaped_hotels_base (
            //             KATEGORI, TESIS_ADI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, LATITUDE, LONGITUDE, WEBSITE_LISTED, URL, SEO_URL, OTEL_ISLETME_NUMARASI, TTS_TESIS_ID
            //         ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            //     `;
            //     return db.run(insertQuery, [
            //         row.KATEGORI, row.TESIS_ADI, row.BOLGE_ADI, row.ALT_BOLGE_ADI, row.BOLGE_DETAY, row.LATITUDE, row.LONGITUDE, row.WEBSITE_LISTED, row.URL, row.SEO_URL, row.OTEL_ISLETME_NUMARASI, row.TTS_TESIS_ID
            //     ]);
            // });
            await Promise.all(insertPromises);
            console.log(`q_tesis_listesi verileri scaped_hotels_base tablosuna eklendi.`);
        }
        /*
        SELECT 
            TTS_TESIS_ID, KATEGORI, TESIS_ADI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, LATITUDE, LONGITUDE, WEBSITE_LISTED, URL, SEO_URL,  OTEL_ISLETME_NUMARASI
        FROM q_tesis_listesi
        where 1=1
        --and BOLGE_ADI = 'İzmir' 
        and ALT_BOLGE_ADI = 'Çeşme' 
        and WEBSITE_LISTED = 'TRUE';
    */
 


    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
